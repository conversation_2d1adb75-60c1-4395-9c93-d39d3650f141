# Deep Reinforcement Learning for Crypto Trading
[![Reinforcement Learning for Crypto Trading](https://github.com/xkaple00/deep-reinforcement-learning-for-crypto-trading/assets/40427300/56adbcde-e049-424c-b2df-9719405de023)](https://www.youtube.com/watch?v=elY9TdrdpgI)

This repository accompanies my blog series: https://medium.com/@sane.ai/deep-reinforcement-learning-for-crypto-trading-72c06bb9b04c <br />


## Part 0: Introduction
Set up: <br />
git clone https://github.com/xkaple00/deep-reinforcement-learning-for-crypto-trading.git <br />
cd deep-reinforcement-learning-for-crypto-trading <br />
conda env create -f environment.yml <br />

Add your API keys to keys.py <br />

## Part 1: Data preparation
Jupyter: <br />
dataset.ipynb <br />

## Part 2: Strategy:
Environment: <br />
./envs/training_env.py <br />

## Part 3: Training
Command to start training: <br />
python train.py <br />

Tensorboard logs example: <br />
https://drive.google.com/file/d/12IyS3PKTx0KQr-J28vYOYJon5qpIRhsB/view <br />

## Part 4: Backtesting
Jupyter notebook: <br />
backtest.ipynb <br />
