name: sane_ai_4
channels:
  - conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1=conda_forge
  - _openmp_mutex=4.5=2_gnu
  - anyio=3.6.2=pyhd8ed1ab_0
  - argon2-cffi=21.3.0=pyhd8ed1ab_0
  - argon2-cffi-bindings=21.2.0=py39hb9d737c_3
  - asttokens=2.2.1=pyhd8ed1ab_0
  - attrs=22.2.0=pyh71513ae_0
  - babel=2.11.0=pyhd8ed1ab_0
  - backcall=0.2.0=pyh9f0ad1d_0
  - backports=1.0=pyhd8ed1ab_3
  - backports.functools_lru_cache=1.6.4=pyhd8ed1ab_0
  - beautifulsoup4=4.11.1=pyha770c72_0
  - bleach=6.0.0=pyhd8ed1ab_0
  - brotlipy=0.7.0=py39hb9d737c_1005
  - ca-certificates=2022.12.7=ha878542_0
  - certifi=2022.12.7=pyhd8ed1ab_0
  - cffi=1.14.6=py39he32792d_0
  - charset-normalizer=2.1.1=pyhd8ed1ab_0
  - comm=0.1.2=pyhd8ed1ab_0
  - cryptography=39.0.0=py39hd598818_0
  - cudatoolkit=11.2.2=hbe64b41_11
  - debugpy=1.6.6=py39h227be39_0
  - decorator=5.1.1=pyhd8ed1ab_0
  - defusedxml=0.7.1=pyhd8ed1ab_0
  - entrypoints=0.4=pyhd8ed1ab_0
  - executing=1.2.0=pyhd8ed1ab_0
  - flit-core=3.8.0=pyhd8ed1ab_0
  - idna=3.4=pyhd8ed1ab_0
  - importlib-metadata=6.0.0=pyha770c72_0
  - importlib_metadata=6.0.0=hd8ed1ab_0
  - importlib_resources=5.10.2=pyhd8ed1ab_0
  - ipykernel=6.21.0=pyh210e3f2_0
  - ipython=8.9.0=pyh41d4057_0
  - ipython_genutils=0.2.0=py_1
  - jedi=0.18.2=pyhd8ed1ab_0
  - jinja2=3.1.2=pyhd8ed1ab_1
  - json5=0.9.5=pyh9f0ad1d_0
  - jsonschema=4.17.3=pyhd8ed1ab_0
  - jupyter_client=8.0.1=pyhd8ed1ab_0
  - jupyter_core=5.2.0=py39hf3d152e_0
  - jupyter_events=0.6.3=pyhd8ed1ab_0
  - jupyter_server=2.1.0=pyhd8ed1ab_0
  - jupyter_server_terminals=0.4.4=pyhd8ed1ab_1
  - jupyterlab=3.5.2=pyhd8ed1ab_0
  - jupyterlab_pygments=0.2.2=pyhd8ed1ab_0
  - jupyterlab_server=2.19.0=pyhd8ed1ab_0
  - ld_impl_linux-64=2.40=h41732ed_0
  - libffi=3.3=h58526e2_2
  - libgcc-ng=12.2.0=h65d4601_19
  - libgomp=12.2.0=h65d4601_19
  - libsodium=1.0.18=h36c2ea0_1
  - libsqlite=3.40.0=h753d276_0
  - libstdcxx-ng=12.2.0=h46fd767_19
  - libzlib=1.2.13=h166bdaf_4
  - markupsafe=2.1.2=py39h72bdee0_0
  - matplotlib-inline=0.1.6=pyhd8ed1ab_0
  - mistune=2.0.4=pyhd8ed1ab_0
  - nbclassic=0.4.8=pyhd8ed1ab_0
  - nbclient=0.7.2=pyhd8ed1ab_0
  - nbconvert=7.2.9=pyhd8ed1ab_0
  - nbconvert-core=7.2.9=pyhd8ed1ab_0
  - nbconvert-pandoc=7.2.9=pyhd8ed1ab_0
  - nbformat=5.7.3=pyhd8ed1ab_0
  - ncurses=6.3=h27087fc_1
  - nest-asyncio=1.5.6=pyhd8ed1ab_0
  - notebook=6.5.2=pyha770c72_1
  - notebook-shim=0.2.2=pyhd8ed1ab_0
  - openssl=1.1.1t=h0b41bf4_0
  - packaging=23.0=pyhd8ed1ab_0
  - pandoc=2.19.2=h32600fe_1
  - pandocfilters=1.5.0=pyhd8ed1ab_0
  - parso=0.8.3=pyhd8ed1ab_0
  - pexpect=4.8.0=pyh1a96a4e_2
  - pickleshare=0.7.5=py_1003
  - pip=22.3.1=py39h06a4308_0
  - pkgutil-resolve-name=1.3.10=pyhd8ed1ab_0
  - platformdirs=2.6.2=pyhd8ed1ab_0
  - prometheus_client=0.16.0=pyhd8ed1ab_0
  - prompt-toolkit=3.0.36=pyha770c72_0
  - psutil=5.9.4=py39hb9d737c_0
  - ptyprocess=0.7.0=pyhd3deb0d_0
  - pure_eval=0.2.2=pyhd8ed1ab_0
  - pycparser=2.21=pyhd8ed1ab_0
  - pygments=2.14.0=pyhd8ed1ab_0
  - pyopenssl=23.0.0=pyhd8ed1ab_0
  - pyrsistent=0.19.3=py39h72bdee0_0
  - pysocks=1.7.1=pyha2e5f31_6
  - python=3.9.13=haa1d7c7_1
  - python-dateutil=2.8.2=pyhd8ed1ab_0
  - python-fastjsonschema=2.16.2=pyhd8ed1ab_0
  - python-json-logger=2.0.4=pyhd8ed1ab_0
  - python_abi=3.9=2_cp39
  - pyyaml=6.0=py39hb9d737c_5
  - pyzmq=25.0.0=py39h0be026e_0
  - readline=8.1.2=h0f457ee_0
  - rfc3339-validator=0.1.4=pyhd8ed1ab_0
  - rfc3986-validator=0.1.1=pyh9f0ad1d_0
  - send2trash=1.8.0=pyhd8ed1ab_0
  - setuptools=66.1.1=pyhd8ed1ab_0
  - six=1.16.0=pyh6c4a22f_0
  - sniffio=1.3.0=pyhd8ed1ab_0
  - soupsieve=2.3.2.post1=pyhd8ed1ab_0
  - sqlite=3.40.0=h4ff8645_0
  - stack_data=0.6.2=pyhd8ed1ab_0
  - terminado=0.17.1=pyh41d4057_0
  - tinycss2=1.2.1=pyhd8ed1ab_0
  - tk=8.6.12=h27826a3_0
  - tomli=2.0.1=pyhd8ed1ab_0
  - tornado=6.2=py39hb9d737c_1
  - traitlets=5.9.0=pyhd8ed1ab_0
  - typing-extensions=4.4.0=hd8ed1ab_0
  - typing_extensions=4.4.0=pyha770c72_0
  - urllib3=1.26.14=pyhd8ed1ab_0
  - wcwidth=0.2.6=pyhd8ed1ab_0
  - webencodings=0.5.1=py_1
  - websocket-client=1.5.0=pyhd8ed1ab_0
  - wheel=0.38.4=pyhd8ed1ab_0
  - xz=5.2.6=h166bdaf_0
  - yaml=0.2.5=h7f98852_2
  - zeromq=4.3.4=h9c3ff4c_1
  - zipp=3.12.0=pyhd8ed1ab_0
  - zlib=1.2.13=h166bdaf_4
  - pip:
    - absl-py==1.4.0
    - aiohttp==3.8.3
    - aiosignal==1.3.1
    - apscheduler==3.10.4
    - astunparse==1.6.3
    - async-timeout==4.0.2
    - backports-zoneinfo==0.2.1
    - boto3==1.26.59
    - botocore==1.29.59
    - browser-cookie3==0.19.1
    - cachetools==5.2.1
    - click==8.0.4
    - cloudpickle==2.2.1
    - cycler==0.11.0
    - dateparser==1.1.6
    - distlib==0.3.6
    - empyrical==0.5.5
    - exceptiongroup==1.1.3
    - farama-notifications==0.0.4
    - filelock==3.9.0
    - flatbuffers==23.3.3
    - fonttools==4.38.0
    - frozenlist==1.3.3
    - fsspec==2023.1.0
    - gast==0.4.0
    - gdown==4.6.0
    - glassnode==0.0.2
    - google-api-core==2.11.0
    - google-auth==2.16.0
    - google-auth-oauthlib==0.4.6
    - google-cloud-core==2.3.2
    - google-cloud-storage==2.8.0
    - google-crc32c==1.5.0
    - google-pasta==0.2.0
    - google-resumable-media==2.5.0
    - googleapis-common-protos==1.59.0
    - greenlet==2.0.2
    - grpcio==1.43.0
    - gym==0.21.0
    - gym-notices==0.0.8
    - gym-unity==0.27.0
    - gymnasium==0.26.3
    - gymnasium-notices==0.0.1
    - h11==0.14.0
    - h5py==3.7.0
    - imageio==2.24.0
    - iso8601==1.1.0
    - jeepney==0.8.0
    - jmespath==1.0.1
    - joblib==1.2.0
    - jwt==1.3.1
    - keras==2.11.0
    - keras-preprocessing==1.1.2
    - kiwisolver==1.4.4
    - libclang==15.0.6.1
    - lxml==4.9.2
    - lz4==4.0.2
    - markdown==3.4.1
    - markdown-it-py==3.0.0
    - matplotlib==3.5.3
    - mdurl==0.1.2
    - mlagents-envs==0.27.0
    - modin==0.18.1
    - msgpack==1.0.4
    - multidict==6.0.4
    - networkx==3.0
    - numpy==1.23.3
    - nvidia-cublas-cu11==11.11.3.6
    - nvidia-cudnn-cu11==8.6.0.163
    - oauthlib==3.2.2
    - opencv-python==4.7.0.68
    - opt-einsum==3.3.0
    - outcome==1.2.0
    - pandas==1.5.3
    - pandas-datareader==0.10.0
    - pandas-ta==0.3.14b0
    - pathspec==0.10.3
    - patsy==0.5.3
    - pillow==9.2.0
    - playwright==1.38.0
    - protobuf==3.19.6
    - pyarrow==15.0.0
    - pyasn1==0.4.8
    - pyasn1-modules==0.2.8
    - pybit==5.3.0
    - pycryptodomex==3.19.0
    - pyee==9.0.4
    - pyfolio==0.9.2
    - pyparsing==3.0.9
    - python-binance==1.0.16
    - python-dotenv==1.0.0
    - pytz==2023.3.post1
    - pytz-deprecation-shim==0.1.0.post0
    - pywavelets==1.4.1
    - ray==2.3.1
    - regex==2022.10.31
    - requests==2.28.1
    - requests-oauthlib==1.3.1
    - rich==13.7.0
    - rsa==4.9
    - s3transfer==0.6.0
    - sanpy==0.11.4
    - scikit-image==0.19.3
    - scikit-learn==1.2.0
    - scipy==1.9.1
    - seaborn==0.12.2
    - selenium==4.13.0
    - sortedcontainers==2.4.0
    - statsmodels==0.13.5
    - tabulate==0.8.10
    - tensor-annotations==2.0.2
    - tensorboard==2.11.2
    - tensorboard-data-server==0.6.1
    - tensorboard-plugin-wit==1.8.1
    - tensorboardx==2.6
    - tensorflow==2.11.0
    - tensorflow-estimator==2.11.0
    - tensorflow-io-gcs-filesystem==0.32.0
    - tensorflow-probability==0.19.0
    - termcolor==2.2.0
    - threadpoolctl==3.1.0
    - tifffile==2022.10.10
    - torch==1.8.1
    - torchaudio==0.8.1
    - torchvision==0.9.1
    - tqdm==4.64.1
    - trio==0.22.2
    - trio-websocket==0.11.1
    - typer==0.9.0
    - tzdata==2022.7
    - tzlocal==4.2
    - ujson==5.7.0
    - virtualenv==20.17.1
    - webdriver-manager==4.0.1
    - websockets==10.4
    - werkzeug==2.2.2
    - wrapt==1.14.1
    - wsproto==1.2.0
    - yarl==1.8.2
