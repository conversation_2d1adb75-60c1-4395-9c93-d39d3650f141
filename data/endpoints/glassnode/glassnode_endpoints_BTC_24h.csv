,"endpoints","urls"
0,"addresses/new_non_zero_count","https://api.glassnode.com/v1/metrics/addresses/new_non_zero_count?a={coin}&i={resolution}&s={start_time_unix}&u={end_time_unix}"
1,"addresses/profit_relative","https://api.glassnode.com/v1/metrics/addresses/profit_relative?a={coin}&i={resolution}&s={start_time_unix}&u={end_time_unix}"
2,"derivatives/futures_annualized_basis_3m","https://api.glassnode.com/v1/metrics/derivatives/futures_annualized_basis_3m?a={coin}&i={resolution}&s={start_time_unix}&u={end_time_unix}"
3,"derivatives/futures_estimated_leverage_ratio","https://api.glassnode.com/v1/metrics/derivatives/futures_estimated_leverage_ratio?a={coin}&i={resolution}&s={start_time_unix}&u={end_time_unix}"
4,"derivatives/futures_funding_rate_perpetual","https://api.glassnode.com/v1/metrics/derivatives/futures_funding_rate_perpetual?a={coin}&i={resolution}&s={start_time_unix}&u={end_time_unix}"
5,"derivatives/futures_liquidated_volume_long_relative","https://api.glassnode.com/v1/metrics/derivatives/futures_liquidated_volume_long_relative?a={coin}&i={resolution}&s={start_time_unix}&u={end_time_unix}"
6,"derivatives/futures_liquidated_volume_long_sum","https://api.glassnode.com/v1/metrics/derivatives/futures_liquidated_volume_long_sum?a={coin}&c=native&i={resolution}&s={start_time_unix}&u={end_time_unix}"
7,"derivatives/futures_liquidated_volume_short_sum","https://api.glassnode.com/v1/metrics/derivatives/futures_liquidated_volume_short_sum?a={coin}&c=native&i={resolution}&s={start_time_unix}&u={end_time_unix}"
8,"derivatives/futures_open_interest_perpetual_sum","https://api.glassnode.com/v1/metrics/derivatives/futures_open_interest_perpetual_sum?a={coin}&c=native&i={resolution}&s={start_time_unix}&u={end_time_unix}"
9,"derivatives/futures_open_interest_sum","https://api.glassnode.com/v1/metrics/derivatives/futures_open_interest_sum?a={coin}&c=native&i={resolution}&s={start_time_unix}&u={end_time_unix}"
10,"derivatives/futures_volume_daily_perpetual_sum","https://api.glassnode.com/v1/metrics/derivatives/futures_volume_daily_perpetual_sum?a={coin}&c=native&i={resolution}&s={start_time_unix}&u={end_time_unix}"
11,"derivatives/futures_volume_daily_sum","https://api.glassnode.com/v1/metrics/derivatives/futures_volume_daily_sum?a={coin}&c=native&i={resolution}&s={start_time_unix}&u={end_time_unix}"
12,"derivatives/options_open_interest_put_call_ratio","https://api.glassnode.com/v1/metrics/derivatives/options_open_interest_put_call_ratio?a={coin}&i={resolution}&s={start_time_unix}&u={end_time_unix}&e=aggregated"
13,"derivatives/options_open_interest_sum","https://api.glassnode.com/v1/metrics/derivatives/options_open_interest_sum?a={coin}&c=native&i={resolution}&s={start_time_unix}&u={end_time_unix}"
14,"derivatives/options_volume_daily_sum","https://api.glassnode.com/v1/metrics/derivatives/options_volume_daily_sum?a={coin}&c=native&i={resolution}&s={start_time_unix}&u={end_time_unix}"
15,"derivatives/options_volume_put_call_ratio","https://api.glassnode.com/v1/metrics/derivatives/options_volume_put_call_ratio?a={coin}&i={resolution}&s={start_time_unix}&u={end_time_unix}&e=aggregated"
16,"indicators/net_unrealized_profit_loss","https://api.glassnode.com/v1/metrics/indicators/net_unrealized_profit_loss?a={coin}&i={resolution}&s={start_time_unix}&u={end_time_unix}"
17,"indicators/nupl_less_155","https://api.glassnode.com/v1/metrics/indicators/nupl_less_155?a={coin}&i={resolution}&s={start_time_unix}&u={end_time_unix}"
18,"indicators/nupl_more_155","https://api.glassnode.com/v1/metrics/indicators/nupl_more_155?a={coin}&i={resolution}&s={start_time_unix}&u={end_time_unix}"
19,"indicators/realized_loss","https://api.glassnode.com/v1/metrics/indicators/realized_loss?a={coin}&i={resolution}&s={start_time_unix}&u={end_time_unix}"
20,"indicators/realized_profit","https://api.glassnode.com/v1/metrics/indicators/realized_profit?a={coin}&i={resolution}&s={start_time_unix}&u={end_time_unix}"
21,"indicators/realized_profit_loss_ratio","https://api.glassnode.com/v1/metrics/indicators/realized_profit_loss_ratio?a={coin}&i={resolution}&s={start_time_unix}&u={end_time_unix}"
22,"indicators/realized_profits_to_value_ratio","https://api.glassnode.com/v1/metrics/indicators/realized_profits_to_value_ratio?a={coin}&i={resolution}&s={start_time_unix}&u={end_time_unix}"
23,"indicators/sopr","https://api.glassnode.com/v1/metrics/indicators/sopr?a={coin}&i={resolution}&s={start_time_unix}&u={end_time_unix}"
24,"indicators/unrealized_loss","https://api.glassnode.com/v1/metrics/indicators/unrealized_loss?a={coin}&i={resolution}&s={start_time_unix}&u={end_time_unix}"
25,"indicators/unrealized_profit","https://api.glassnode.com/v1/metrics/indicators/unrealized_profit?a={coin}&i={resolution}&s={start_time_unix}&u={end_time_unix}"
26,"indicators/velocity","https://api.glassnode.com/v1/metrics/indicators/velocity?a={coin}&i={resolution}&s={start_time_unix}&u={end_time_unix}"
27,"market/mvrv","https://api.glassnode.com/v1/metrics/market/mvrv?a={coin}&i={resolution}&s={start_time_unix}&u={end_time_unix}"
28,"market/mvrv_less_155","https://api.glassnode.com/v1/metrics/market/mvrv_less_155?a={coin}&i={resolution}&s={start_time_unix}&u={end_time_unix}"
29,"market/mvrv_more_155","https://api.glassnode.com/v1/metrics/market/mvrv_more_155?a={coin}&i={resolution}&s={start_time_unix}&u={end_time_unix}"
30,"market/mvrv_z_score","https://api.glassnode.com/v1/metrics/market/mvrv_z_score?a={coin}&i={resolution}&s={start_time_unix}&u={end_time_unix}"
31,"supply/profit_relative","https://api.glassnode.com/v1/metrics/supply/profit_relative?a={coin}&i={resolution}&s={start_time_unix}&u={end_time_unix}"
32,"transactions/transfers_volume_exchanges_net","https://api.glassnode.com/v1/metrics/transactions/transfers_volume_exchanges_net?a={coin}&i={resolution}&s={start_time_unix}&u={end_time_unix}"
33,"transactions/transfers_volume_from_exchanges_sum","https://api.glassnode.com/v1/metrics/transactions/transfers_volume_from_exchanges_sum?a={coin}&i={resolution}&s={start_time_unix}&u={end_time_unix}"
34,"transactions/transfers_volume_sum","https://api.glassnode.com/v1/metrics/transactions/transfers_volume_sum?a={coin}&i={resolution}&s={start_time_unix}&u={end_time_unix}"
35,"transactions/transfers_volume_to_exchanges_sum","https://api.glassnode.com/v1/metrics/transactions/transfers_volume_to_exchanges_sum?a={coin}&i={resolution}&s={start_time_unix}&u={end_time_unix}"
